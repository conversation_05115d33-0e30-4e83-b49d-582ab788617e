# ===================================================================
# SOCIAL MEDIA AND OAUTH CONFIGURATION
# ===================================================================
spring:
  config:
    activate:
      on-profile: dev

google:
  credentials: ${workdir:.}/config/google-credentials.json
  token: tokens
  spreadsheet: { id: null }
  clientId: ************-io62a9nofs3sk04i1s7beplaoqhvv16h.apps.googleusercontent.com
  clientSecret: 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
  redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
  user-info-uri: https://www.googleapis.com/oauth2/v3/userinfo
  service-account:
    # Path to service account key file (JSON)
    key-file: ${GOOGLE_SERVICE_ACCOUNT_KEY_FILE:${workdir:.}/config/google-credentials.json}
    # Email to impersonate (for Gmail API)
    user-email: ${GOOGLE_SERVICE_ACCOUNT_USER_EMAIL:<EMAIL>}
  scope: openid,profile,email

# WordPress common configuration
wordpress:
  username: crawlbot
  url: 'http://localhost:8068/blog'
  password: eD7ZW#^Q&h2*gAlrbi@%&7v%

# Facebook API common configuration
facebook:
  client-id: ***************
  client-secret: 7721bb1cfe1c8118e39aa0a95fe87379
  redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
  scope: public_profile,email,pages_show_list,pages_read_engagement,pages_manage_posts,pages_manage_engagement,pages_read_insights,pages_manage_comments
  user-info-uri: https://graph.facebook.com/me
  authorization-base-url: https://www.facebook.com/v12.0/dialog/oauth
  token-base-url: https://graph.facebook.com/v12.0/oauth/access_token
  api-base-url: https://graph.facebook.com/v12.0
  max-retries: 3
  connect-timeout: 30000
  read-timeout: 30000
---
spring:
  config:
    activate:
      on-profile: prod
  security:
    oauth2:
      client:
        registration:
          google:
            clientSecret: ${GOOGLE_CLIENT_SECRET}
            clientId: ${GOOGLE_CLIENT_ID}
          facebook:
            client-id: ${FACEBOOK_CLIENT_ID}
            client-secret: ${FACEBOOK_CLIENT_SECRET}

# Google configuration for production
google:
  spreadsheetId: ${GOOGLE_SPREADSHEET_ID}
  clientSecret: ${GOOGLE_CLIENT_SECRET}
  clientId: ${GOOGLE_CLIENT_ID}

# WordPress configuration for production
wordpress:
  url: ${WORDPRESS_URL}
  password: ${WORDPRESS_PASSWORD}

# Facebook API configuration for production
facebook:
  api:
    connect-timeout: 30000
    read-timeout: 30000
  oauth:
    client-id: ${FACEBOOK_CLIENT_ID}
    client-secret: ${FACEBOOK_CLIENT_SECRET}
