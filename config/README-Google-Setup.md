# Google Sheets API Setup Guide

## Issue: UserCredentials instance cannot refresh because there is no refresh token

This error occurs when the Google OAuth2 authentication is not properly configured with refresh tokens. Here are the solutions:

## Solution 1: Use Service Account (Recommended)

1. **Create a Service Account in Google Cloud Console:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Select your project or create a new one
   - Navigate to "IAM & Admin" > "Service Accounts"
   - Click "Create Service Account"
   - Fill in the details and click "Create"
   - Grant the service account "Editor" role or specific Google Sheets API permissions
   - Click "Done"

2. **Generate and Download Service Account Key:**
   - Click on the created service account
   - Go to "Keys" tab
   - Click "Add Key" > "Create new key"
   - Choose "JSON" format
   - Download the key file

3. **Configure the Application:**
   - Rename the downloaded file to `google-service-account.json`
   - Place it in the `config/` directory
   - Update `application-social.yml`:
     ```yaml
     google:
       service-account:
         key-file: ${workdir:.}/config/google-service-account.json
     ```

4. **Enable Google Sheets API:**
   - In Google Cloud Console, go to "APIs & Services" > "Library"
   - Search for "Google Sheets API"
   - Click on it and enable it

## Solution 2: Fix OAuth2 User Credentials (Alternative)

If you prefer to use OAuth2 user credentials instead of service account:

1. **Ensure Proper OAuth2 Configuration:**
   - Make sure your OAuth2 client is configured for "Desktop application" type
   - Include `offline` access type in the authorization flow
   - Request `refresh_token` in the initial authorization

2. **Re-authorize the Application:**
   - Delete any existing token files in the `tokens/` directory
   - Restart the application
   - Complete the OAuth2 authorization flow when prompted
   - Make sure to grant all requested permissions

## Current Configuration

The application is configured to:
1. First try to load service account credentials
2. Fall back to OAuth2 user credentials if service account fails
3. Use application default credentials as last resort

## Troubleshooting

- **File not found errors**: Make sure the service account key file exists at the specified path
- **Permission errors**: Ensure the service account has proper permissions for Google Sheets API
- **API not enabled**: Enable Google Sheets API in Google Cloud Console
- **Invalid credentials**: Regenerate the service account key or re-authorize OAuth2 flow
